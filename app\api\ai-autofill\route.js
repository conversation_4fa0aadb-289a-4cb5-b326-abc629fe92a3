import OpenAI from 'openai';

// Initialize OpenAI client
const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY || process.env.NEXT_PUBLIC_OPENAI_API_KEY
});

export async function POST(request) {
    try {
        const { type, eventData, context } = await request.json();

        if (type === 'finish-job') {
            return await handleFinishJobAutofill(eventData, context);
        } else if (type === 'payment-description') {
            return await handlePaymentDescriptionAutofill(eventData, context);
        } else {
            return new Response(
                JSON.stringify({ error: 'Invalid type specified' }),
                { status: 400, headers: { 'Content-Type': 'application/json' } }
            );
        }
    } catch (error) {
        console.error('AI Autofill Error:', error);
        return new Response(
            JSON.stringify({ error: 'Failed to generate AI response' }),
            { status: 500, headers: { 'Content-Type': 'application/json' } }
        );
    }
}

async function handleFinishJobAutofill(eventData, context) {
    const systemMessage = {
        role: 'system',
        content: `You are an AI assistant for Detail On The Go mobile detailing service. Your job is to analyze event/job information and auto-fill a job completion form with intelligent suggestions.

Based on the event information provided, extract and suggest appropriate values for the following fields:
- Vehicle: Extract vehicle information from the event description
- Customer Name: Extract customer name if available
- Customer Phone: Extract phone number if available  
- Customer Email: Extract email if available
- Customer Address: Extract address if available (could be from event location)
- Package: Extract service package information
- Payment Collected: Suggest most likely payment method (Card, Cash, Check, Other)
- Notes: Generate helpful completion notes based on the service type

Return your response as a JSON object with these exact field names:
{
  "vehicle": "extracted or suggested vehicle info",
  "customerName": "extracted customer name or empty string",
  "customerPhone": "extracted phone or empty string", 
  "customerEmail": "extracted email or empty string",
  "customerAddress": "extracted/suggested address or empty string",
  "package": "extracted package info or empty string",
  "paymentCollected": "Card|Cash|Check|Other",
  "notes": "helpful completion notes"
}

Be conservative with extractions - only fill fields if you're confident about the information. Use empty strings for uncertain fields.`
    };

    const userMessage = {
        role: 'user',
        content: `Please analyze this event and suggest auto-fill values for the job completion form:

Event Title: ${eventData.title || 'N/A'}
Event Description: ${eventData.description || 'N/A'}
Event Location: ${eventData.location || 'N/A'}
Event Start: ${eventData.start ? new Date(eventData.start).toLocaleString() : 'N/A'}
Event End: ${eventData.end ? new Date(eventData.end).toLocaleString() : 'N/A'}

Additional Context: ${context || 'None provided'}`
    };

    const completion = await openai.chat.completions.create({
        model: 'gpt-4-turbo',
        messages: [systemMessage, userMessage],
        max_tokens: 500,
        temperature: 0.3,
        response_format: { type: "json_object" }
    });

    const aiResponse = completion.choices[0].message.content.trim();
    const suggestions = JSON.parse(aiResponse);

    return new Response(
        JSON.stringify({ suggestions }),
        { status: 200, headers: { 'Content-Type': 'application/json' } }
    );
}

async function handlePaymentDescriptionAutofill(eventData, context) {
    const systemMessage = {
        role: 'system',
        content: `You are an AI assistant for Detail On The Go mobile detailing service. Your job is to generate professional, detailed payment descriptions for invoices and receipts.

Based on the event information and payment context provided, create a comprehensive payment description that includes:
- Service details
- Vehicle information
- Package/plan information
- Location details
- Professional formatting

The description should be clear, professional, and suitable for customer invoices. Include all relevant service details but keep it concise and well-formatted.

Return your response as a JSON object:
{
  "description": "the generated payment description",
  "summary": "a brief one-line summary"
}`
    };

    const userMessage = {
        role: 'user',
        content: `Please generate a professional payment description for this service:

Event Title: ${eventData.title || 'N/A'}
Event Description: ${eventData.description || 'N/A'}
Event Location: ${eventData.location || 'N/A'}
Event Start: ${eventData.start ? new Date(eventData.start).toLocaleString() : 'N/A'}

Payment Context: ${JSON.stringify(context, null, 2)}

Generate a professional payment description that would be appropriate for a customer invoice.`
    };

    const completion = await openai.chat.completions.create({
        model: 'gpt-4-turbo',
        messages: [systemMessage, userMessage],
        max_tokens: 400,
        temperature: 0.3,
        response_format: { type: "json_object" }
    });

    const aiResponse = completion.choices[0].message.content.trim();
    const result = JSON.parse(aiResponse);

    return new Response(
        JSON.stringify(result),
        { status: 200, headers: { 'Content-Type': 'application/json' } }
    );
}
