'use client';

import React, { useState, useRef } from 'react';

const FinishJobPopup = ({ isOpen, onClose, selectedEvent, onJobFinished }) => {
  if (!isOpen) return null;
  const [formData, setFormData] = useState({
    vehicle: '',
    customerName: '',
    customerPhone: '',
    customerEmail: '',
    customerAddress: '',
    package: '',
    paymentCollected: 'Card',
    notes: '',
    photos: []
  });

  const [isAutoFilling, setIsAutoFilling] = useState(false);
  const fileInputRef = useRef(null);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleAIAutofill = async () => {
    if (!selectedEvent) return;

    setIsAutoFilling(true);
    try {
      const response = await fetch('/api/ai-autofill', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'finish-job',
          eventData: selectedEvent,
          context: 'Auto-filling job completion form'
        })
      });

      if (!response.ok) {
        throw new Error('Failed to get AI suggestions');
      }

      const { suggestions } = await response.json();

      // Update form data with AI suggestions, but don't overwrite existing user input
      setFormData(prev => ({
        ...prev,
        vehicle: prev.vehicle || suggestions.vehicle || '',
        customerName: prev.customerName || suggestions.customerName || '',
        customerPhone: prev.customerPhone || suggestions.customerPhone || '',
        customerEmail: prev.customerEmail || suggestions.customerEmail || '',
        customerAddress: prev.customerAddress || suggestions.customerAddress || '',
        package: prev.package || suggestions.package || '',
        paymentCollected: prev.paymentCollected === 'Card' ? (suggestions.paymentCollected || 'Card') : prev.paymentCollected,
        notes: prev.notes || suggestions.notes || ''
      }));

    } catch (error) {
      console.error('AI Autofill Error:', error);
      // Fallback: Basic auto-fill from event data without AI
      const basicSuggestions = extractBasicInfo(selectedEvent);
      setFormData(prev => ({
        ...prev,
        vehicle: prev.vehicle || basicSuggestions.vehicle || '',
        customerName: prev.customerName || basicSuggestions.customerName || '',
        customerPhone: prev.customerPhone || basicSuggestions.customerPhone || '',
        customerEmail: prev.customerEmail || basicSuggestions.customerEmail || '',
        customerAddress: prev.customerAddress || basicSuggestions.customerAddress || '',
        package: prev.package || basicSuggestions.package || '',
        notes: prev.notes || basicSuggestions.notes || ''
      }));
    } finally {
      setIsAutoFilling(false);
    }
  };

  const extractBasicInfo = (event) => {
    if (!event) return {};

    const description = event.description || '';
    const title = event.title || '';
    const location = event.location || '';

    // Basic extraction without AI
    const vehicle = description.match(/Vehicle:\s*\(([^)]+)\)/)?.[1] ||
                   description.match(/Vehicle:\s*([^\n]+)/)?.[1] || '';
    const customerName = description.match(/Customer:\s*([^\n]+)/)?.[1] ||
                        description.match(/Name:\s*([^\n]+)/)?.[1] || '';
    const phone = description.match(/Phone:\s*([^\n]+)/)?.[1] ||
                 description.match(/\b\d{3}[-.]?\d{3}[-.]?\d{4}\b/)?.[0] || '';
    const email = description.match(/Email:\s*([^\s]+@[^\s]+)/)?.[1] ||
                 description.match(/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/)?.[0] || '';
    const pkg = description.match(/Package:\s*([^\n]+)/)?.[1] || '';

    return {
      vehicle: vehicle.trim(),
      customerName: customerName.trim(),
      customerPhone: phone.trim(),
      customerEmail: email.trim(),
      customerAddress: location.trim(),
      package: pkg.trim(),
      notes: `Job completed for: ${title}`
    };
  };

  const handlePhotoUpload = (event) => {
    const files = Array.from(event.target.files);
    const photoPromises = files.map(file => {
      return new Promise((resolve) => {
        const reader = new FileReader();
        reader.onload = (e) => {
          resolve({
            file,
            preview: e.target.result,
            name: file.name,
            size: file.size
          });
        };
        reader.readAsDataURL(file);
      });
    });

    Promise.all(photoPromises).then(photos => {
      setFormData(prev => ({
        ...prev,
        photos: [...prev.photos, ...photos]
      }));
    });
  };

  const removePhoto = (index) => {
    setFormData(prev => ({
      ...prev,
      photos: prev.photos.filter((_, i) => i !== index)
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    // Validate required fields
    if (!formData.vehicle.trim()) {
      alert('Vehicle is required');
      return;
    }

    // Prepare job completion data
    const jobData = {
      ...formData,
      eventId: selectedEvent?.id,
      eventTitle: selectedEvent?.title,
      eventStart: selectedEvent?.start,
      eventLocation: selectedEvent?.location,
      completedAt: new Date().toISOString(),
      photos: formData.photos.map(photo => ({
        name: photo.name,
        size: photo.size,
        data: photo.preview // Base64 data
      }))
    };

    onJobFinished(jobData);
  };

  const resetForm = () => {
    setFormData({
      vehicle: '',
      customerName: '',
      customerPhone: '',
      customerEmail: '',
      customerAddress: '',
      package: '',
      paymentCollected: 'Card',
      notes: '',
      photos: []
    });
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-gray-800 bg-opacity-50 flex justify-center items-center z-50">
      <div className="bg-white p-6 rounded-lg w-[90%] max-w-2xl shadow-lg relative max-h-[80vh] overflow-y-auto">
        <button 
          onClick={handleClose} 
          className="absolute top-3 right-3 text-2xl hover:text-gray-600"
        >
          &times;
        </button>
        
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-xl font-semibold text-gray-900">Finish Job</h3>
          {/* Temporarily disabled AI button for debugging */}
          {/*
          <button
            type="button"
            onClick={handleAIAutofill}
            disabled={isAutoFilling}
            className="px-3 py-1 bg-purple-500 text-white text-sm rounded-md hover:bg-purple-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-1"
          >
            {isAutoFilling ? (
              <>
                <div className="w-3 h-3 border border-white border-t-transparent rounded-full animate-spin"></div>
                AI Filling...
              </>
            ) : (
              <>
                ✨ AI Auto-fill
              </>
            )}
          </button>
          */}
        </div>

        {selectedEvent && (
          <div className="mb-4 p-3 bg-gray-50 rounded-lg">
            <h4 className="font-medium text-gray-700">Event Details:</h4>
            <p className="text-sm text-gray-600">{selectedEvent.title}</p>
            {selectedEvent.location && (
              <p className="text-sm text-gray-600">Location: {selectedEvent.location}</p>
            )}
            {selectedEvent.start && (
              <p className="text-sm text-gray-600">
                Date: {new Date(selectedEvent.start).toLocaleString()}
              </p>
            )}
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Vehicle - Required */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Vehicle <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              value={formData.vehicle}
              onChange={(e) => handleInputChange('vehicle', e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter vehicle information"
              required
            />
          </div>

          {/* Customer Name - Optional */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Customer Name
            </label>
            <input
              type="text"
              value={formData.customerName}
              onChange={(e) => handleInputChange('customerName', e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter customer name"
            />
          </div>

          {/* Customer Phone - Optional */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Customer Phone
            </label>
            <input
              type="tel"
              value={formData.customerPhone}
              onChange={(e) => handleInputChange('customerPhone', e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter customer phone"
            />
          </div>

          {/* Customer Email - Optional */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Customer Email
            </label>
            <input
              type="email"
              value={formData.customerEmail}
              onChange={(e) => handleInputChange('customerEmail', e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter customer email"
            />
          </div>

          {/* Customer Address - Optional */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Customer Address
            </label>
            <textarea
              value={formData.customerAddress}
              onChange={(e) => handleInputChange('customerAddress', e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter customer address"
              rows="2"
            />
          </div>

          {/* Package - Optional */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Package
            </label>
            <input
              type="text"
              value={formData.package}
              onChange={(e) => handleInputChange('package', e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter package details"
            />
          </div>

          {/* Payment Collected */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Payment Collected
            </label>
            <select
              value={formData.paymentCollected}
              onChange={(e) => handleInputChange('paymentCollected', e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="Card">Card</option>
              <option value="Cash">Cash</option>
              <option value="Check">Check</option>
              <option value="Other">Other</option>
            </select>
          </div>

          {/* Photo Upload */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Photos
            </label>
            <input
              type="file"
              ref={fileInputRef}
              onChange={handlePhotoUpload}
              multiple
              accept="image/*"
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
            
            {/* Photo Previews */}
            {formData.photos.length > 0 && (
              <div className="mt-2 grid grid-cols-2 sm:grid-cols-3 gap-2">
                {formData.photos.map((photo, index) => (
                  <div key={index} className="relative">
                    <img
                      src={photo.preview}
                      alt={`Preview ${index + 1}`}
                      className="w-full h-20 object-cover rounded border"
                    />
                    <button
                      type="button"
                      onClick={() => removePhoto(index)}
                      className="absolute -top-1 -right-1 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs hover:bg-red-600"
                    >
                      ×
                    </button>
                    <p className="text-xs text-gray-500 mt-1 truncate">{photo.name}</p>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Notes */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Notes
            </label>
            <textarea
              value={formData.notes}
              onChange={(e) => handleInputChange('notes', e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter any additional notes"
              rows="3"
            />
          </div>

          {/* Submit Buttons */}
          <div className="flex gap-3 pt-4">
            <button
              type="button"
              onClick={handleClose}
              className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:ring-2 focus:ring-gray-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="flex-1 px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 focus:ring-2 focus:ring-green-500"
            >
              Finish Job
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default FinishJobPopup;
