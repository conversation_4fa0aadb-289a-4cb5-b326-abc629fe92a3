'use client';

import Link from 'next/link';
import { useState, useEffect, Fragment } from 'react';
import { signInWithGoogle, firebaseSignOut } from '../auth';
import { useAuthState } from 'react-firebase-hooks/auth';
import { useDocument } from 'react-firebase-hooks/firestore';
import { auth, db } from '../lib/firebase/firebase';
import { doc, setDoc } from 'firebase/firestore';
import { useRouter } from 'next/navigation';
import { Menu, Transition } from '@headlessui/react';
import NextImage from 'next/image';
import FirstTimeLoginModal from './FirstTimeLoginModal';
import PricingPopup from '../app/pricing/pricing-popup';

const AuthButton = ({ children, onClick }) => (
  <button
    onClick={onClick}
    className="px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg hover:opacity-90 transition-all duration-200 shadow-md"
  >
    {children}
  </button>
);

const Header = () => {
  const [user, loading, error] = useAuthState(auth);
  const [userDoc] = useDocument(user ? doc(db, 'users', user.uid) : null);
  const isAdmin = userDoc?.data()?.isAdmin;
  const [showModal, setShowModal] = useState(false);
  const [showPricingPopup, setShowPricingPopup] = useState(false);
  const router = useRouter();

  const handleLogout = async () => {
    await firebaseSignOut();
    router.push('/');
  };

  const handleFirstTimeLogin = async (formData) => {
    const userRef = doc(db, 'users', user.uid);
    const branchRequested = formData.branch?.endsWith('-requested')
      ? formData.branch
      : `${formData.branch || 'lwr'}-requested`;

    await setDoc(
      userRef,
      {
        address: formData.address || '',
        branch: branchRequested,
        wage: formData.wage || 16,
        userId: user.uid,
        displayName: user.displayName || user.email.split('@')[0],
        email: user.email,
        isAdmin: false,
        firstTimeLogin: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        paymentType: 'hourly',
        commissionRate: 0.27,
      },
      { merge: true }
    );

    setShowModal(false);
  };

  useEffect(() => {
    if (user && userDoc?.data()?.firstTimeLogin) {
      setShowModal(true);
    }
  }, [user, userDoc]);

  const navLinks = [
    { href: '/training', label: 'Training ' },
    { href: '/calendar', label: 'Calendar' },
    { href: '/sms', label: 'Messenger' },
    { href: '/pay-link', label: 'Create Pay Link' },
    // Only include Payments link for admins
    ...(isAdmin ? [{ href: '/payments', label: 'Payments' }] : []),
    { href: isAdmin ? '/admin' : '/time', label: isAdmin ? 'Admin' : 'Time' },
  ];

  return (
    <header className="fixed top-0 left-0 w-full z-50 bg-white/80 backdrop-blur-md border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          <Link href='/' className="flex-shrink-0">
            <NextImage
              src="/logo without hand.png"
              alt="Logo"
              width={200}
              height={40}
              className="w-[150px] sm:w-[175px] hover:opacity-80 transition-opacity"
              priority
            />
          </Link>

          <div className="flex items-center gap-4">
            {user ? (
              <div className="flex items-center gap-4">
                <Menu as="div" className="relative ml-3">
                  {({ open }) => (
                    <>
                      <Menu.Button className="flex items-center gap-2 bg-gray-100 rounded-full p-2 hover:bg-gray-200 transition-colors">
                        <span className="sr-only">Open user menu</span>
                        <div className={`h-8 w-8 rounded-full overflow-hidden transition-transform duration-300 ${open ? 'rotate-100' : 'rotate-0'}`}>
                          <NextImage
                            src="/hand logo.png"
                            alt="User Menu"
                            width={32}
                            height={32}
                            className="w-full h-full object-cover"
                          />
                        </div>
                      </Menu.Button>
                      <Transition
                        as={Fragment}
                        enter="transition ease-out duration-100"
                        enterFrom="transform opacity-0 scale-95"
                        enterTo="transform opacity-100 scale-100"
                        leave="transition ease-in duration-75"
                        leaveFrom="transform opacity-100 scale-100"
                        leaveTo="transform opacity-0 scale-95"
                      >
                        <Menu.Items className="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                          {navLinks.map((link) => (
                            <Menu.Item key={link.href}>
                              {({ active }) => (
                                <Link
                                  href={link.href}
                                  className={`${active ? 'bg-gray-100' : ''} block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100`}
                                >
                                  {link.label}
                                </Link>
                              )}
                            </Menu.Item>
                          ))}
                          <div className="border-t border-gray-200 my-1"></div>
                          <div className="border-t border-gray-200 my-1"></div>
                          <Menu.Item>
                            {({ active }) => (
                              <button
                                onClick={handleLogout}
                                className={`${active ? 'bg-gray-100' : ''} w-full text-left px-4 py-2 text-sm text-gray-700`}
                              >
                                Sign out
                              </button>
                            )}
                          </Menu.Item>
                        </Menu.Items>
                      </Transition>
                    </>
                  )}
                </Menu>
              </div>
            ) : (
              <AuthButton onClick={() => signInWithGoogle(setShowModal)}>
                Login
              </AuthButton>
            )}
          </div>
        </div>
      </div>

      {showModal && user && (
        <FirstTimeLoginModal
          user={user}
          onClose={() => setShowModal(false)}
          onSubmit={handleFirstTimeLogin}
        />
      )}

      {/* Pricing Popup */}
      <PricingPopup
        isOpen={showPricingPopup}
        onClose={() => setShowPricingPopup(false)}
      />
    </header>
  );
};

export default Header;
