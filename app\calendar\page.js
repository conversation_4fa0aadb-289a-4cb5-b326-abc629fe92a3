'use client';

import { useState, useEffect } from 'react';
import { onAuthStateChangedListener } from '../../auth';
import React, { lazy, Suspense } from 'react';
import { doc, getDoc } from 'firebase/firestore';
import { auth, db } from '../../lib/firebase/firebase'; // Update path as needed

// Lazy imports with error boundaries
const PaymentPopup = lazy(() => import('./PaymentPopup').catch(err => {
  console.error('Failed to load PaymentPopup:', err);
  return { default: () => <div>Error loading Payment component</div> };
}));

const MessengerPopup = lazy(() => import('../sms/sms-pop').catch(err => {
  console.error('Failed to load MessengerPopup:', err);
  return { default: () => <div>Error loading SMS component</div> };
}));

const EmailPopup = lazy(() => import('../email/email-popup').catch(err => {
  console.error('Failed to load EmailPopup:', err);
  return { default: () => <div>Error loading Email component</div> };
}));

const EditEventPopup = lazy(() => import('./EditEventPopup').catch(err => {
  console.error('Failed to load EditEventPopup:', err);
  return { default: () => <div>Error loading Edit component</div> };
}));

// Regular import for FinishJobPopup (since it's not lazy loaded)
import FinishJobPopup from './FinishJobPopup';

const CalendarPage = () => {
  const [events, setEvents] = useState([]);
  const [currentDate, setCurrentDate] = useState(new Date());
  const [view, setView] = useState('month');
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [isPopupVisible, setIsPopupVisible] = useState(false);
  const [stripeCustomerId, setStripeCustomerId] = useState('');
  const [isPaymentPopupVisible, setIsPaymentPopupVisible] = useState(false);
  const [isSmsPopupVisible, setIsSmsPopupVisible] = useState(false);
  const [isEmailPopupVisible, setIsEmailPopupVisible] = useState(false);
  const [isFinishJobPopupVisible, setIsFinishJobPopupVisible] = useState(false);
  const [clientPhone, setClientPhone] = useState('');
  const [clientName, setClientName] = useState('');
  const [clientEmail, setClientEmail] = useState('');
  const [businessNumber, setBusinessNumber] = useState('');
  const [branch, setBranch] = useState('');
  const [invoiceDetails, setInvoiceDetails] = useState(null);
  const [isEditPopupVisible, setIsEditPopupVisible] = useState(false);
  const [userName, setUserName] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [userDoc, setUserDoc] = useState(null);
  const [locationDetails, setLocationDetails] = useState(null);

  const getCurrentUser = async () => {
    return new Promise((resolve, reject) => {
      try {
        const unsubscribe = onAuthStateChangedListener(async (user) => {
          try {
            if (user) {
              const docRef = doc(db, 'users', user.uid);
              const docSnap = await getDoc(docRef);
              if (docSnap.exists()) {
                setUserDoc(docSnap.data());
              } else {
                console.warn('User document does not exist');
                setUserDoc({});
              }
            }
            unsubscribe();
            resolve(user);
          } catch (error) {
            console.error('Error getting user document:', error);
            unsubscribe();
            reject(error);
          }
        });
      } catch (error) {
        console.error('Error setting up auth listener:', error);
        reject(error);
      }
    });
  };

  useEffect(() => {
    const initializeUserData = async () => {
      try {
        const user = await getCurrentUser();
        if (user && userDoc) {
          setBusinessNumber(userDoc.address || '');
          setBranch(userDoc.branch || '');
          setUserName(user.displayName || '');
        }
      } catch (error) {
        console.error('Error initializing user data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initializeUserData();
  }, [userDoc]);

  useEffect(() => {
    const fetchEvents = async () => {
      if (!userDoc?.branch) {
        setEvents([]);
        return;
      }

      try {
        const response = await fetch(
          `https://dotg-team-calendar-896343340170.us-central1.run.app?branch=${encodeURIComponent(userDoc.branch)}`
        );

        if (!response.ok) {
          throw new Error(`Failed to fetch events: ${response.status} ${response.statusText}`);
        }

        const text = await response.text();
        let data;

        try {
          data = JSON.parse(text);
        } catch (parseError) {
          console.error('Failed to parse response as JSON:', parseError);
          throw new Error('Invalid JSON response from server');
        }

        setLocationDetails({
          businessNumber: data.businessNumber || '',
          collectionId: data.collectionId,
          location: userDoc.branch,
        });

        if (!data.events || !Array.isArray(data.events)) {
          console.warn('Invalid response format: "events" array not found');
          setEvents([]);
          return;
        }

        setEvents(
          data.events.map((event) => ({
            id: event.id,
            title: event.summary || 'Untitled Event',
            start: new Date(event.start?.dateTime || event.start?.date),
            end: event.end ? new Date(event.end.dateTime || event.end.date) : null,
            description: event.description || '',
            location: event.location || '',
          }))
        );
      } catch (error) {
        console.error('Error fetching events:', error);
        setEvents([]);
        setLocationDetails(null);
      }
    };

    if (userDoc?.branch) {
      fetchEvents();
    }
  }, [userDoc?.branch]);

  const handleNext = () => {
    const newDate = new Date(currentDate);
    if (view === 'month') {
      newDate.setMonth(newDate.getMonth() + 1);
    } else if (view === 'week') {
      newDate.setDate(newDate.getDate() + 7);
    } else {
      newDate.setDate(newDate.getDate() + 1);
    }
    setCurrentDate(newDate);
  };

  const handlePrev = () => {
    const newDate = new Date(currentDate);
    if (view === 'month') {
      newDate.setMonth(newDate.getMonth() - 1);
    } else if (view === 'week') {
      newDate.setDate(newDate.getDate() - 7);
    } else {
      newDate.setDate(newDate.getDate() - 1);
    }
    setCurrentDate(newDate);
  };

  const formatMonthYear = () =>
    currentDate.toLocaleDateString('en-US', { year: 'numeric', month: 'long' });

  const formatWeekRange = () => {
    const start = getStartOfWeek(currentDate);
    const end = new Date(start);
    end.setDate(end.getDate() + 6);
    return `${start.toLocaleDateString()} - ${end.toLocaleDateString()}`;
  };

  const getStartOfWeek = (date) => {
    const newDate = new Date(date);
    const dayOfWeek = newDate.getDay();
    newDate.setDate(newDate.getDate() - dayOfWeek);
    return newDate;
  };

  const getWeekDays = (date) => {
    const start = getStartOfWeek(date);
    const days = [];
    for (let i = 0; i < 7; i++) {
      const day = new Date(start);
      day.setDate(day.getDate() + i);
      days.push(day);
    }
    return days;
  };

  const getMonthDaysWithOffset = (date) => {
    const startOfMonth = new Date(date.getFullYear(), date.getMonth(), 1);
    const endOfMonth = new Date(date.getFullYear(), date.getMonth() + 1, 0);
    const days = [];
    const startDayOffset = startOfMonth.getDay();

    for (let i = 0; i < startDayOffset; i++) {
      days.push(null);
    }
    for (let i = 1; i <= endOfMonth.getDate(); i++) {
      days.push(new Date(date.getFullYear(), date.getMonth(), i));
    }
    return days;
  };

  const eventsByDay = (events) =>
    events.reduce((acc, event) => {
      const eventDay = new Date(event.start).toLocaleDateString();
      if (!acc[eventDay]) acc[eventDay] = [];
      acc[eventDay].push(event);
      return acc;
    }, {});

  const groupedEvents = eventsByDay(events);

  const handleEventClick = (event) => {
    console.log('Event clicked:', event.title);
    setClientName(event.title || 'Unknown Client');
    setSelectedEvent(event);
    setIsPopupVisible(true);
  };

  const closePopup = () => {
    setIsPopupVisible(false);
    setSelectedEvent(null);
  };

  const normalizePhoneNumber = (rawNumber) => {
    if (!rawNumber) return null;
    const clean = rawNumber.replace(/[^\d+]/g, '');
    if (clean.startsWith('+')) return clean;
    if (clean.length === 10) return `+1${clean}`;
    if (clean.length === 11 && clean.startsWith('1')) return `+${clean}`;
    return null;
  };

  const extractFromDescription = (pattern) => {
    if (!selectedEvent?.description) return null;
    const stripped = selectedEvent.description.replace(/<[^>]+>/g, ' ');
    const match = stripped.match(pattern);
    return match?.[1]?.trim() || null;
  };

  const handleSmsClick = () => {
    if (!locationDetails?.businessNumber) {
      alert('Business number not configured');
      return;
    }
    if (!locationDetails?.location) {
      alert('Branch not configured');
      return;
    }

    const phone = extractFromDescription(/Phone(?: Number)?:\s*([+\d\s\-()]+)/i);
    if (!phone) {
      alert('No client phone found');
      return;
    }

    const normalizedPhone = normalizePhoneNumber(phone);
    if (!normalizedPhone) {
      alert('Invalid phone format');
      return;
    }

    setBusinessNumber(locationDetails.businessNumber);
    setClientPhone(normalizedPhone);
    setIsSmsPopupVisible(true);
  };

  const handleEmailClick = () => {
    const email = extractFromDescription(/Email:\s*([^\s]+@[^\s]+)/i);
    if (email) {
      setClientEmail(email);
      setIsEmailPopupVisible(true);
    } else {
      alert('No client email found');
    }
  };

  const renderDayCell = (day, isDaily = false) => {
    if (!day) {
      return (
        <div className="p-2 bg-gray-100 dark:bg-gray-900 rounded" style={{ minHeight: '60px' }} />
      );
    }

    const isToday = day.toDateString() === new Date().toDateString();
    const dayEvents = groupedEvents[day.toLocaleDateString()] || [];

    if (isDaily) {
      return (
        <div
          className={`p-2 bg-white dark:bg-gray-800 rounded ${isToday ? 'ring-2 ring-green-500' : ''}`}
          style={{ minHeight: '60px' }}
        >
          <div className="text-sm sm:text-base text-gray-700 dark:text-gray-200 mb-2 font-semibold">
            {day.toDateString()}
          </div>
          <div className="flex flex-col space-y-2">
            {dayEvents.map((event, idx) => {
              const displayTitle = event.title?.trim() || 'Untitled Event';
              return (
                <div
                  key={`${event.id || idx}-${idx}`}
                  onClick={() => handleEventClick(event)}
                  className="w-full block bg-blue-100 hover:bg-blue-200 text-blue-700 rounded-sm px-1 py-0.5 text-xs text-left overflow-hidden cursor-pointer"
                  style={{ whiteSpace: 'nowrap', overflow: 'hidden' }}
                >
                  <div className="font-medium">{displayTitle}</div>
                  {event.start && (
                    <div className="text-xs text-gray-600">
                      Start: {new Date(event.start).toLocaleTimeString([], {
                        hour: '2-digit',
                        minute: '2-digit',
                      })}
                    </div>
                  )}
                  {event.location && (
                    <div className="text-sm text-gray-600 mt-1">{event.location}</div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      );
    }

    return (
      <div
        className={`p-0.5 bg-white dark:bg-gray-800 rounded border border-gray-300 dark:border-gray-700 ${isToday ? 'ring-2 ring-green-500' : ''}`}
        style={{ minHeight: '60px' }}
      >
        <div className="text-xs sm:text-sm text-gray-700 dark:text-gray-200 mb-1">{day.getDate()}</div>
        <div className="flex flex-col space-y-1">
          {dayEvents.map((event, idx) => {
            const displayTitle = event.title?.trim() || 'Untitled Event';
            return (
              <button
                key={`${event.id || idx}-${idx}`}
                onClick={() => handleEventClick(event)}
                className="w-full block bg-blue-100 hover:bg-blue-200 text-blue-700 rounded-sm px-1 py-0.5 text-xs text-left overflow-hidden"
                style={{ whiteSpace: 'nowrap', overflow: 'hidden' }}
              >
                <div className="font-medium">{displayTitle}</div>
                {event.start && (
                  <div className="text-xs text-gray-600">
                    {new Date(event.start).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  </div>
                )}
              </button>
            );
          })}
        </div>
      </div>
    );
  };

  // Error boundary for the component
  if (isLoading) {
    return (
      <div className="bg-white dark:bg-gray-900 w-full min-h-[100vh]">
        <div className="flex justify-center items-center h-screen">
          <p>Loading...</p>
        </div>
      </div>
    );
  }

  if (!userDoc?.branch) {
    return (
      <div className="bg-white dark:bg-gray-900 w-full min-h-[100vh]">
        <div className="flex justify-center items-center h-screen">
          <p>Please complete your profile setup to access this page.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-900 w-full min-h-[100vh]">
      <div className="bg-white dark:bg-gray-900 pt-16 pb-12 px-1 sm:px-6 max-w-screen-xl mx-auto">
        <h2 className="text-lg sm:text-xl font-semibold text-gray-900 dark:text-white mb-4 text-center">
          {userName ? `${userName}'s Calendar` : 'Calendar'} -{' '}
          {view === 'month'
            ? formatMonthYear()
            : view === 'week'
              ? formatWeekRange()
              : currentDate.toLocaleDateString()}
        </h2>

        <div className="flex items-center justify-between w-full mb-4 px-2 sm:px-4">
          <button onClick={handlePrev} className="px-3 py-2 bg-blue-500 text-white rounded text-2xl">
            👈
          </button>
          <div className="flex space-x-2">
            <button
              onClick={() => setView('month')}
              className={`px-3 py-2 rounded ${view === 'month' ? 'bg-blue-600 text-white' : 'bg-gray-200'}`}
            >
              Month
            </button>
            <button
              onClick={() => setView('week')}
              className={`px-3 py-2 rounded ${view === 'week' ? 'bg-blue-600 text-white' : 'bg-gray-200'}`}
            >
              Week
            </button>
            <button
              onClick={() => setView('day')}
              className={`px-3 py-2 rounded ${view === 'day' ? 'bg-blue-600 text-white' : 'bg-gray-200'}`}
            >
              Day
            </button>
          </div>
          <button onClick={handleNext} className="px-3 py-2 bg-blue-500 text-white rounded text-2xl">
            👉
          </button>
        </div>

        {view === 'month' && (
          <div className="grid grid-cols-7 gap-x-0 gap-y-0 w-full text-center">
            {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day, i) => (
              <div
                key={i}
                className="font-medium text-sm sm:text-base text-gray-900 dark:text-white py-1 bg-gray-200 dark:bg-gray-800 rounded"
              >
                {day}
              </div>
            ))}
            {getMonthDaysWithOffset(currentDate).map((day, i) => (
              <React.Fragment key={i}>{renderDayCell(day)}</React.Fragment>
            ))}
          </div>
        )}

        {view === 'week' && (
          <div className="grid grid-cols-7 gap-x-0 gap-y-0 w-full text-center">
            {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day, i) => (
              <div
                key={i}
                className="font-medium text-sm sm:text-base text-gray-900 dark:text-white py-1 bg-gray-200 dark:bg-gray-800 rounded"
              >
                {day}
              </div>
            ))}
            {getWeekDays(currentDate).map((day, i) => (
              <React.Fragment key={i}>{renderDayCell(day)}</React.Fragment>
            ))}
          </div>
        )}

        {view === 'day' && <div className="w-full text-center">{renderDayCell(currentDate, true)}</div>}

        {isPopupVisible && selectedEvent && (
          <div className="fixed inset-0 bg-gray-800 bg-opacity-50 flex justify-center items-center z-50">
            <div className="relative bg-white p-6 rounded-lg w-full h-full sm:w-[90%] sm:h-auto sm:max-h-[80vh] shadow-lg overflow-y-auto">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-xl font-semibold text-gray-900">{selectedEvent.title}</h3>
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => setIsEditPopupVisible(true)}
                    className="text-blue-600 hover:text-blue-700"
                    title="Edit Event"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-6 w-6"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                      />
                    </svg>
                  </button>
                  <button
                    onClick={closePopup}
                    className="rounded-lg px-4 py-2 bg-red-500 hover:bg-red-600 text-white"
                  >
                    Close
                  </button>
                </div>
              </div>
              <p className="text-gray-800">
                <strong>Start:</strong> {new Date(selectedEvent.start).toLocaleString()}
              </p>
              <p className="text-gray-800">
                <strong>End:</strong>{' '}
                {selectedEvent.end ? new Date(selectedEvent.end).toLocaleString() : 'N/A'}
              </p>
              {selectedEvent.location && (
                <p className="text-gray-800 mt-2">
                  <strong>Location:</strong> {selectedEvent.location}
                </p>
              )}
              {selectedEvent.description && (
                <div className="text-gray-800 mt-2">
                  <strong>Description:</strong>
                  <div
                    className="whitespace-pre-wrap mt-2"
                    dangerouslySetInnerHTML={{
                      __html: selectedEvent.description
                        .replace(/<br>/g, '\n')
                        .replace(/<a href="(.+?)" target="_blank">(.+?)<\/a>/g, '$2 ($1)')
                        .replace(/&/g, '&')
                        .replace(/<u>(.+?)<\/u>/g, '<strong>$1</strong>'),
                    }}
                  />
                </div>
              )}
              <div className="mt-4 flex flex-wrap gap-2">
                <button
                  onClick={() => {
                    const stripped = selectedEvent.description?.replace(/<[^>]+>/g, ' ') || '';
                    const match = stripped.match(/Stripe(?: Customer ID)?:\s*(cus_[a-zA-Z0-9]+)/);
                    setStripeCustomerId(match?.[1]?.trim() || '');
                    setIsPaymentPopupVisible(true);
                  }}
                  className="flex-1 bg-blue-500 text-white px-4 py-1 rounded-lg hover:bg-blue-600 text-sm"
                >
                  View Payments & Charge
                </button>
                <button
                  onClick={handleSmsClick}
                  className="flex-1 bg-blue-400 text-white px-4 py-1 rounded-lg hover:bg-blue-500 text-sm"
                >
                  Send SMS
                </button>
                <button
                  onClick={handleEmailClick}
                  className="flex-1 bg-blue-300 text-white px-4 py-1 rounded-lg hover:bg-blue-400 text-sm"
                >
                  Send Email
                </button>
                <button
                  onClick={() => setIsFinishJobPopupVisible(true)}
                  className="flex-1 bg-green-500 text-white px-4 py-1 rounded-lg hover:bg-green-600 text-sm"
                >
                  Finish Job
                </button>
              </div>
            </div>
          </div>
        )}

        {isEditPopupVisible && (
          <Suspense fallback={<div>Loading editor...</div>}>
            <EditEventPopup
              event={selectedEvent}
              onClose={() => setIsEditPopupVisible(false)}
              onSave={async (updatedEvent) => {
                try {
                  const user = await getCurrentUser();
                  const toRFC3339 = (datetime) => {
                    const date = new Date(datetime);
                    const offset = -date.getTimezoneOffset();
                    const pad = (n) => String(n).padStart(2, '0');
                    return `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())}T${pad(date.getHours())}:${pad(date.getMinutes())}:00${offset >= 0 ? '+' : '-'}${pad(Math.abs(offset) / 60)}:${pad(Math.abs(offset) % 60)}`;
                  };
                  const requestBody = {
                    eventId: updatedEvent.id,
                    summary: updatedEvent.title,
                    start: toRFC3339(updatedEvent.start),
                    end: toRFC3339(updatedEvent.end),
                    description: updatedEvent.description,
                    location: updatedEvent.location,
                    recurrence: updatedEvent.recurrence,
                    timeZone: updatedEvent.timeZone,
                  };
                  console.log('Data being sent to backend:', requestBody);
                  const response = await fetch(
                    `https://us-central1-dotg-d6313.cloudfunctions.net/universal-modify-event?email=${user.email}`,
                    {
                      method: 'POST',
                      headers: { 'Content-Type': 'application/json' },
                      body: JSON.stringify(requestBody),
                    }
                  );
                  if (!response.ok) throw new Error('Failed to update event');
                  setEvents(
                    events.map((e) =>
                      e.id === updatedEvent.id
                        ? {
                          ...e,
                          ...updatedEvent,
                          start: new Date(updatedEvent.start),
                          end: updatedEvent.end ? new Date(updatedEvent.end) : null,
                        }
                        : e
                    )
                  );
                  setSelectedEvent((prev) =>
                    prev?.id === updatedEvent.id
                      ? {
                        ...prev,
                        ...updatedEvent,
                        start: new Date(updatedEvent.start),
                        end: updatedEvent.end ? new Date(updatedEvent.end) : null,
                      }
                      : prev
                  );
                  setIsEditPopupVisible(false);
                } catch (error) {
                  console.error('Error updating event:', error);
                  alert(`Failed to update event: ${error.message}`);
                }
              }}
            />
          </Suspense>
        )}

        {isPaymentPopupVisible && (
          <Suspense fallback={<div>Loading payment...</div>}>
            <PaymentPopup
              branch={branch}
              onClose={() => setIsPaymentPopupVisible(false)}
              businessNumber={businessNumber}
              clientName={clientName}
              clientNumber={clientPhone}
              stripeCustomerId={stripeCustomerId}
              selectedEvent={selectedEvent}
              onPaymentSuccess={(details) => {
                setClientEmail(details.customerEmail);
                setInvoiceDetails(details);
                setIsEmailPopupVisible(true);
              }}
            />
          </Suspense>
        )}

        {isSmsPopupVisible && (
          <Suspense fallback={<div>Loading SMS...</div>}>
            <MessengerPopup
              isOpen={isSmsPopupVisible}
              onClose={() => setIsSmsPopupVisible(false)}
              businessNumber={locationDetails?.businessNumber || ''}
              clientNumber={clientPhone}
              location={locationDetails?.location || ''}
            />
          </Suspense>
        )}

        {isEmailPopupVisible && (
          <Suspense fallback={<div>Loading Email...</div>}>
            <EmailPopup
              isOpen={isEmailPopupVisible}
              onClose={() => setIsEmailPopupVisible(false)}
              clientEmail={clientEmail}
              clientName={clientName}
              invoiceDetails={invoiceDetails}
            />
          </Suspense>
        )}

        {isFinishJobPopupVisible && (
          <FinishJobPopup
            isOpen={isFinishJobPopupVisible}
            onClose={() => setIsFinishJobPopupVisible(false)}
            selectedEvent={selectedEvent}
            onJobFinished={(jobData) => {
              console.log('Job finished:', jobData);
              setIsFinishJobPopupVisible(false);
              // TODO: Send to Firebase when ready
            }}
          />
        )}
      </div>
    </div>
  );
};

export default CalendarPage;